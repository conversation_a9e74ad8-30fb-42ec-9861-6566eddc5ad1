#!/usr/bin/env node

/**
 * 自动生成游戏配置文件
 * 扫描 public/games 目录下的游戏文件，生成 games.json 配置
 */

const fs = require('fs');
const path = require('path');

const gamesDir = path.join(__dirname, '../public/games');
const configFile = path.join(gamesDir, 'games.json');

// 系统图标映射
const systemIcons = {
  gb: '🎮',
  gbc: '🎮',
  gba: '🎮',
  nes: '🍄',
  snes: '🎯',
  md: '🦔',
  gen: '🦔',
  sms: '🎮',
  n64: '🎮',
  psx: '💿'
};

// 根据文件扩展名推断系统类型
function getSystemFromExtension(filename) {
  const ext = path.extname(filename).toLowerCase().slice(1);
  const systemMap = {
    gb: 'gb',
    gbc: 'gbc', 
    gba: 'gba',
    nes: 'nes',
    smc: 'snes',
    sfc: 'snes',
    md: 'md',
    gen: 'md',
    sms: 'sms',
    n64: 'n64',
    v64: 'n64',
    z64: 'n64',
    bin: 'psx',
    cue: 'psx'
  };
  
  // 对于 ZIP 文件，尝试从文件名推断
  if (ext === 'zip') {
    const name = filename.toLowerCase();
    if (name.includes('mario') || name.includes('马里奥')) return 'nes';
    if (name.includes('kirby') || name.includes('卡比')) return 'gb';
    if (name.includes('sonic') || name.includes('索尼克')) return 'md';
    return 'gb'; // 默认为 Game Boy
  }
  
  return systemMap[ext] || 'unknown';
}

// 生成游戏名称（移除扩展名和特殊字符）
function generateGameName(filename) {
  return path.basename(filename, path.extname(filename));
}

// 扫描游戏目录
function scanGamesDirectory() {
  if (!fs.existsSync(gamesDir)) {
    console.log('游戏目录不存在，创建目录:', gamesDir);
    fs.mkdirSync(gamesDir, { recursive: true });
    return [];
  }

  const files = fs.readdirSync(gamesDir);
  const gameFiles = files.filter(file => {
    const ext = path.extname(file).toLowerCase();
    return ['.gb', '.gbc', '.gba', '.nes', '.smc', '.sfc', '.md', '.gen', '.sms', '.n64', '.v64', '.z64', '.bin', '.cue', '.zip'].includes(ext);
  });

  return gameFiles.map(filename => {
    const system = getSystemFromExtension(filename);
    const name = generateGameName(filename);
    
    return {
      name,
      path: `/games/${filename}`,
      description: `经典的${name}游戏`,
      system,
      icon: systemIcons[system] || '🎮'
    };
  });
}

// 生成配置文件
function generateConfig() {
  console.log('正在扫描游戏目录:', gamesDir);
  
  const games = scanGamesDirectory();
  
  const config = {
    games,
    generated: new Date().toISOString(),
    version: '1.0.0'
  };

  fs.writeFileSync(configFile, JSON.stringify(config, null, 2), 'utf8');
  
  console.log(`✅ 已生成游戏配置文件: ${configFile}`);
  console.log(`📁 发现 ${games.length} 个游戏文件:`);
  games.forEach(game => {
    console.log(`   ${game.icon} ${game.name} (${game.system})`);
  });
}

// 如果直接运行此脚本
if (require.main === module) {
  generateConfig();
}

module.exports = { generateConfig, scanGamesDirectory };
