<template>
  <div class="retroarch-emulator">
    <div class="emulator-container">
      <div v-if="loading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <h3>正在加载RetroArch模拟器...</h3>
          <p>正在下载 {{ core?.name }} 核心...</p>
        </div>
      </div>

      <div id="canvas" class="game-canvas"></div>

      <div class="controls">
        <button @click="startGame" class="btn-start" v-if="!loading">
          🎮 启动游戏
        </button>
        <button @click="stopEmulator" class="btn-stop">⏹️ 停止游戏</button>
        <button @click="$emit('back')" class="btn-back">← 返回选择</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import type { GameFile, EmulatorCore } from "../types/emulator";

const props = defineProps<{
  game: GameFile;
  core: EmulatorCore | null;
  loading: boolean;
}>();

const emit = defineEmits<{
  error: [message: string];
  stop: [];
  back: [];
}>();

const loading = ref(true);

onMounted(async () => {
  if (props.core && props.game) {
    try {
      await initializeRetroArch();
    } catch (error) {
      console.error("RetroArch初始化失败:", error);
      emit("error", "RetroArch初始化失败: " + (error as Error).message);
    }
  }
});

async function initializeRetroArch() {
  try {
    loading.value = true;

    console.log("开始加载EmulatorJS模拟器...");

    // 设置EmulatorJS配置
    const gameUrl = await createGameBlob();
    const coreType = getEmulatorJSCore(props.core?.id || "gambatte");

    console.log("游戏URL:", gameUrl);
    console.log("核心类型:", coreType);

    // 设置回调函数（必须在加载器之前设置）
    (window as any).EJS_ready = () => {
      console.log("🎮 EmulatorJS ready!");
      loading.value = false;
    };

    (window as any).EJS_onGameStart = () => {
      console.log("🎮 Game started!");
      loading.value = false;
    };

    (window as any).EJS_onLoadState = () => {
      console.log("🎮 Game loaded!");
    };

    // 清理之前的全局变量
    const ejsKeys = Object.keys(window).filter((k) => k.startsWith("EJS_"));
    ejsKeys.forEach((key) => delete (window as any)[key]);

    // 配置EmulatorJS全局变量
    (window as any).EJS_player = "#canvas";
    (window as any).EJS_core = coreType;
    (window as any).EJS_pathtodata = "/emulatorjs/";
    (window as any).EJS_gameUrl = gameUrl;
    (window as any).EJS_gameName = props.game.name;
    (window as any).EJS_startOnLoaded = false;
    (window as any).EJS_threads = false;
    (window as any).EJS_volume = 0.8;

    console.log("EmulatorJS配置:", {
      player: (window as any).EJS_player,
      core: (window as any).EJS_core,
      pathtodata: (window as any).EJS_pathtodata,
      gameUrl: (window as any).EJS_gameUrl,
      gameName: (window as any).EJS_gameName,
    });

    // 动态加载EmulatorJS加载器
    await loadEmulatorJS();
    // 给EmulatorJS一些时间来初始化
    setTimeout(() => {
      if (loading.value) {
        console.log("EmulatorJS初始化超时，尝试手动启动...");
        loading.value = false;
      }
    }, 15000); // 增加到15秒超时
  } catch (error) {
    loading.value = false;
    throw error;
  }
}

function getEmulatorJSCore(coreId: string): string {
  // EmulatorJS核心名称映射 - 直接返回核心名称
  const coreMap: { [key: string]: string } = {
    gambatte: "gambatte",
    mgba: "mgba",
    snes9x: "snes9x",
    fceumm: "fceumm",
    genesis_plus_gx: "genesis_plus_gx",
  };

  return coreMap[coreId] || "gambatte";
}

async function createGameBlob(): Promise<string> {
  try {
    // 将游戏数据转换为Blob URL
    const gameData = new Uint8Array(props.game.data);
    console.log("游戏数据大小:", gameData.length, "字节");

    // 根据文件扩展名设置正确的MIME类型
    const extension = props.game.name.split(".").pop()?.toLowerCase();
    let mimeType = "application/octet-stream";

    if (extension === "zip") {
      mimeType = "application/zip";
    } else if (extension === "gb" || extension === "gbc") {
      mimeType = "application/x-gameboy-rom";
    } else if (extension === "gba") {
      mimeType = "application/x-gba-rom";
    }

    const blob = new Blob([gameData], { type: mimeType });
    const url = URL.createObjectURL(blob);

    console.log("创建Blob URL:", url);
    console.log("MIME类型:", mimeType);

    return url;
  } catch (error) {
    console.error("创建游戏Blob失败:", error);
    throw new Error("无法创建游戏文件");
  }
}

async function loadEmulatorJS() {
  return new Promise<void>((resolve, reject) => {
    // 清理之前的实例
    if ((window as any).EJS_emulator) {
      try {
        (window as any).EJS_emulator.destroy();
      } catch (e) {
        console.log("清理旧实例失败:", e);
      }
      delete (window as any).EJS_emulator;
    }

    // 保存当前配置
    const gameUrl = (window as any).EJS_gameUrl;
    const coreType = (window as any).EJS_core;

    // 清理并重新设置全局变量
    const ejsKeys = Object.keys(window).filter((k) => k.startsWith("EJS_"));
    ejsKeys.forEach((key) => delete (window as any)[key]);

    // 重新设置配置
    (window as any).EJS_player = "#canvas";
    (window as any).EJS_core = coreType;
    (window as any).EJS_pathtodata = "/emulatorjs/";
    (window as any).EJS_gameUrl = gameUrl;
    (window as any).EJS_startOnLoaded = false;
    (window as any).EJS_threads = false;
    (window as any).EJS_volume = 0.8;

    // 检查是否已经加载了脚本
    const existingScript = document.querySelector(
      'script[src="/emulatorjs/loader.js"]'
    );
    if (existingScript) {
      existingScript.remove();
    }

    const script = document.createElement("script");
    script.src = "/emulatorjs/loader.js";
    script.async = true;

    script.onload = () => {
      console.log("✅ EmulatorJS加载器脚本加载成功");
      // 给脚本一些时间来初始化
      setTimeout(() => {
        if ((window as any).EJS_emulator) {
          console.log("✅ EmulatorJS初始化成功");

          // 添加事件监听器
          console.log("🎮 模拟器准备就绪");
          // 尝试自动启动游戏
          setTimeout(() => {
            try {
              console.log("尝试自动启动游戏...");
              // 查找并点击开始按钮
              const startButton = document.querySelector(".ejs_start_button");
              if (startButton) {
                console.log("找到开始按钮，自动点击...");
                (startButton as HTMLElement).click();
              } else {
                console.log("未找到开始按钮");
              }
            } catch (error) {
              console.log("自动启动游戏失败:", error);
            }
          }, 1000);

          (window as any).EJS_emulator.on("start", () => {
            console.log("🎮 游戏开始");
            loading.value = false;
          });

          (window as any).EJS_emulator.on("error", (error: any) => {
            console.error("❌ EmulatorJS错误:", error);
            loading.value = false;
            emit("error", `模拟器错误: ${error}`);
          });

          resolve();
        } else {
          console.log("等待EmulatorJS初始化...");
          resolve(); // 即使没有立即初始化也继续
        }
      }, 3000); // 增加到3秒等待时间
    };

    script.onerror = (error) => {
      console.error("❌ EmulatorJS加载器脚本加载失败:", error);
      reject(new Error("无法加载EmulatorJS加载器"));
    };

    document.head.appendChild(script);
  });
}

function startGame() {
  try {
    if ((window as any).EJS_emulator) {
      console.log("手动启动游戏...");
      // 尝试不同的启动方法
      if ((window as any).EJS_emulator.startGame) {
        (window as any).EJS_emulator.startGame();
      } else if (
        (window as any).EJS_emulator.gameManager &&
        (window as any).EJS_emulator.gameManager.start
      ) {
        (window as any).EJS_emulator.gameManager.start();
      } else {
        // 模拟点击开始按钮
        const startButton = document.querySelector(".ejs_start_button");
        if (startButton) {
          console.log("点击开始按钮...");
          (startButton as HTMLElement).click();
        } else {
          console.log("未找到开始按钮");
        }
      }
    } else {
      console.log("模拟器未准备就绪");
    }
  } catch (error) {
    console.error("启动游戏失败:", error);
  }
}

function stopEmulator() {
  try {
    // 清理EmulatorJS
    if ((window as any).EJS_emulator) {
      (window as any).EJS_emulator.destroy();
    }

    // 清理全局变量
    delete (window as any).EJS_player;
    delete (window as any).EJS_core;
    delete (window as any).EJS_pathtodata;
    delete (window as any).EJS_gameUrl;
    delete (window as any).EJS_onGameStart;
    delete (window as any).EJS_onLoadState;

    console.log("✅ 模拟器已停止");
  } catch (error) {
    console.error("停止模拟器时出错:", error);
  }
  emit("stop");
}

onUnmounted(() => {
  stopEmulator();
});
</script>

<style scoped>
.retroarch-emulator {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.emulator-container {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  z-index: 10;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.loading-content h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.loading-content p {
  opacity: 0.8;
}

.game-canvas {
  width: 100%;
  height: 400px;
  background: #000;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  display: block;
}

.controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.controls button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.9rem;
}

.controls button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.btn-stop {
  background: rgba(244, 67, 54, 0.2) !important;
  border-color: rgba(244, 67, 54, 0.3) !important;
}

.btn-stop:hover {
  background: rgba(244, 67, 54, 0.3) !important;
}

.btn-back {
  background: rgba(108, 117, 125, 0.2) !important;
  border-color: rgba(108, 117, 125, 0.3) !important;
}

.btn-back:hover {
  background: rgba(108, 117, 125, 0.3) !important;
}

.btn-start {
  background: rgba(76, 175, 80, 0.2) !important;
  border-color: rgba(76, 175, 80, 0.3) !important;
}

.btn-start:hover {
  background: rgba(76, 175, 80, 0.3) !important;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
