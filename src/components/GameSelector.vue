<template>
  <div class="game-selector">
    <h3>🎮 选择游戏</h3>
    <div class="games-grid">
      <div
        v-for="game in availableGames"
        :key="game.path"
        class="game-card"
        @click="selectGame(game)"
      >
        <div class="game-icon">{{ game.icon || "🎮" }}</div>
        <div class="game-name">{{ game.name }}</div>
        <div v-if="game.description" class="game-description">
          {{ game.description }}
        </div>
        <div v-if="game.system" class="game-system">
          {{ getSystemName(game.system) }}
        </div>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="loading-spinner"></div>
      <p>正在加载游戏...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  getAvailableGames,
  loadLocalGame,
  type GameInfo,
} from "../utils/gameLoader";
import type { GameFile } from "../types/emulator";

const emit = defineEmits<{
  gameSelected: [game: GameFile];
  error: [message: string];
}>();

const availableGames = ref<GameInfo[]>([]);
const loading = ref(false);

onMounted(async () => {
  try {
    availableGames.value = await getAvailableGames();
  } catch (error) {
    console.error("获取游戏列表失败:", error);
    emit("error", "获取游戏列表失败");
  }
});

function getSystemName(system: string): string {
  const systemNames: { [key: string]: string } = {
    gb: "Game Boy",
    gbc: "Game Boy Color",
    gba: "Game Boy Advance",
    nes: "Nintendo Entertainment System",
    snes: "Super Nintendo",
    md: "Sega Genesis",
    sms: "Sega Master System",
    n64: "Nintendo 64",
    psx: "PlayStation",
  };
  return systemNames[system] || system.toUpperCase();
}

async function selectGame(gameInfo: GameInfo) {
  loading.value = true;
  try {
    const game = await loadLocalGame(gameInfo.path, gameInfo.name);
    if (game) {
      emit("gameSelected", game);
    } else {
      emit("error", `无法加载游戏: ${gameInfo.name}`);
    }
  } catch (error) {
    console.error("加载游戏失败:", error);
    emit("error", `加载游戏失败: ${(error as Error).message}`);
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.game-selector {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 2rem;
}

.game-selector h3 {
  color: white;
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.game-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.game-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.game-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.game-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.game-description {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.game-system {
  font-size: 0.8rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  display: inline-block;
  opacity: 0.9;
}

.loading {
  text-align: center;
  color: white;
  padding: 2rem;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
